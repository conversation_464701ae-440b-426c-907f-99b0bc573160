import {  getDictList, removeDict } from '@/servers/usecases';
import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import {
  FooterToolbar,
  ModalForm,
  PageContainer,
  ProDescriptions,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import { FormattedMessage, useIntl, useNavigate } from '@umijs/max';
import { Button, Drawer, Input, message, Popconfirm } from 'antd';
import React, { useRef, useState } from 'react';
import type { FormValueType } from './components/UpdateForm';

/**
 * @en-US Add node
 * @zh-CN 添加节点
 * @param fields
 */
const handleAdd = async (fields: API.UsecaseItem) => {
  const hide = message.loading('正在添加');
  try {
    await addTestLLM({ ...fields });
    hide();
    message.success('Added successfully');
    return true;
  } catch (error) {
    hide();
    message.error('Adding failed, please try again!');
    return false;
  }
};

/**
 * @en-US Update node
 * @zh-CN 更新节点
 *
 * @param fields
 */
const handleUpdate = async (fields: any) => {
  const hide = message.loading('Configuring');
  try {
    await updateTestLLM({
      name: fields.name,
      desc: fields.desc,
      key: fields.key,
    });
    hide();

    message.success('Configuration is successful');
    return true;
  } catch (error) {
    hide();
    message.error('Configuration failed, please try again!');
    return false;
  }
};

/**
 *  Delete node
 * @zh-CN 删除节点
 *
 * @param selectedRows
 */
const handleRemove = async (selectedRows: API.UsecaseItem[]) => {
  const hide = message.loading('正在删除');
  if (!selectedRows) return true;
  try {
    
    console.log('selectedRows', selectedRows[0].id);
    
    if (selectedRows.length === 1) {
      const res = await removeDict(selectedRows[0].id,'true');
      hide();
      
      if (res.status === 200) {
        message.success('删除成功');
        return true;
      } else if (res.status === 500) {
        message.error('包含子节点，无法删除');
        return false;
      } else {
        message.error('删除失败，请重试！');
        return false;
      }
    } else {
      // 批量删除时，逐个删除
      let hasError = false;
      let errorMessage = '';
      
      for (const row of selectedRows) {
        const res = await removeDict(row.id, 'true');
        
        if (res.status === 200) {
          continue; // 删除成功，继续下一个
        } else if (res.status === 500) {
          hasError = true;
          errorMessage = `${row.name || row.id} 包含子节点，无法删除`;
          break; // 遇到包含子节点的情况，停止批量删除
        } else {
          hasError = true;
          errorMessage = `删除 ${row.name || row.id} 失败`;
          break; // 遇到其他错误，停止批量删除
        }
      }
      
      hide();
      
      if (hasError) {
        message.error(errorMessage);
        return false;
      } else {
        message.success('批量删除成功');
        return true;
      }
    }
  } catch (error) {
    hide();
    message.error('删除失败，请重试！');
    return false;
  }
};


const Usecase: React.FC = () => {
  /**
   * @en-US Pop-up window of new window
   * @zh-CN 新建窗口的弹窗
   *  */
  const [modalOpen, handleModalOpen] = useState<boolean>(false);

  const [showDetail, setShowDetail] = useState<boolean>(false);

  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.UsecaseItem>();
  const [selectedRowsState, setSelectedRows] = useState<API.UsecaseItem[]>([]);
  const navigate = useNavigate();
  /**
   * @en-US International configuration
   * @zh-CN 国际化配置
   * */
  const intl = useIntl();

  const columns: ProColumns<API.DictItem>[] = [
    
    {
      title: '序号',
      dataIndex: 'id',
      valueType: 'indexBorder',
      width: 60,
      hideInSearch: true,
    },
    {
      title: "数据集名称",
      dataIndex: 'name',
      // hideInSearch: true,
      
    },
    {
      title: "创建时间",
      dataIndex: 'create_time',
      valueType: 'textarea',
      render: (text) => {
        const date = new Date(text);
        return date.toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' });
      },
      search: false
    },
    {
      title: "更新时间",
      dataIndex: 'update_time',
      render: (text) => {
        const date = new Date(text);
        return date.toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' });
      },
      valueType: 'textarea',
      search: false
    },
     {
      title: "操作",
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            navigate(`/usecases/detail/${record.id}`);
          }}
        >
          查看
        </a>,
        <Popconfirm
          key="delete"
          title="确认删除"
          description={`确定要删除数据集"${record.name}"吗？此操作不可撤销。`}
          onConfirm={async () => {
            const success = await handleRemove([record]);
            if (success && actionRef.current) {
              actionRef.current.reload();
            }
          }}
          okText="确认"
          cancelText="取消"
        >
          <a>删除</a>
        </Popconfirm>
      ],
    },
  ];

  function getTemplateList() {
    throw new Error('Function not implemented.');
  }


  return (
    <PageContainer title={false}>
      <ProTable<API.UsecaseItem, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        request={async (params) => {
          const body = {
            limit: params.pageSize || 0,
            offset: params.current,
            keyword: params.name || '',
            module: 'usecase',
          }
          const res = await getDictList(body);
        
          if (res.status === 200) {
            return {
              data: res.data,
              total: res.total || 0,
              success: true
            };

          } else {
            console.error('请求失败:', Error);
            return { data: [], success: false };
          }
        }}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              <FormattedMessage id="pages.searchTable.chosen" defaultMessage="Chosen" />{' '}
              <a style={{ fontWeight: 600 }}>{selectedRowsState.length}</a>{' '}
              <FormattedMessage id="pages.searchTable.item" defaultMessage="项" />
            </div>
          }
        >
          <Button
            onClick={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
            danger
          >
            <FormattedMessage
              id="pages.searchTable.batchDeletion"
              defaultMessage="Batch deletion"
            />
          </Button>
        </FooterToolbar>
      )}

      <ModalForm
        title="创建模型"
        width="420px"
        open={modalOpen}
        onOpenChange={handleModalOpen}
        initialValues={{
          ...currentRow
        }}
        onFinish={async (value) => {
          const success = await handleAdd(value as API.UsecaseItem);
          if (success) {
            handleModalOpen(false);
            if (actionRef.current) {
              actionRef.current.reload();
            }
          }
        }}
      >
        <ProFormText
          label="模型ID"
          name="id"
          rules={[
            {
              required: true,
              message: "模型ID必填项",
            },
          ]}
          width="md"
        />
        <ProFormText
          label="模型名称"
          rules={[
            {
              required: true,
              message: "模型名称必填项",
            },
          ]}
          width="md"
          name="name"
        />
        <ProFormText
          label="模型URL"
          rules={[
            {
              required: true,
              message: "模型URL必填项",
            },
          ]}
          width="md"
          name="api_url"
        />
        <ProFormText
          label="API-KEY"
          rules={[
            {
              required: true,
              message: "API-KEY必填项",
            },
          ]}
          width="md"
          name="api_key"
        />
         <ProFormSelect
          // 使用 request 属性动态获取模板列表
          request={async () => {
            try {
              const response = await getTemplateList(); // 调用获取列表的接口
              return response.data.map((item: any) => ({
                value: item.id.toString(), // 假设模板的 id 作为 value
                label: item.name // 假设模板的 name 作为 label
              }));
            } catch (error) {
              console.error('获取模板列表失败', error);
              return [];
            }
          }}
          rules={[
            {
              required: true,
              message: "API请求模板必填项",
            },
          ]}
          initialValue="openai模板"
          width="md"
          name="api_template_id"
          label="API请求模板"
          debounceTime={300}
        />
        <ProFormTextArea label="描述" width="md" name="description" />
      </ModalForm>

      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.id && (
          <ProDescriptions<API.UsecaseItem>
            column={2}
            title={currentRow?.modelName}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.modelName,
            }}
            columns={columns as ProDescriptionsItemProps<API.UsecaseItem>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default Usecase;
