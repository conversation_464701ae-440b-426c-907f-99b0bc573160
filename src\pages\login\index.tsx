import type { LoginData } from "./model";
import type { FormProps } from "antd";
import type { SideMenu } from "#/public";
import type { AppDispatch } from "@/stores";
import type { ThemeType } from "@/stores/public";
import { message } from "antd";
import { Form, Button, Input } from "antd";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { PASSWORD_RULE, THEME_KEY } from "@/utils/config";
import { login } from "@/servers/login";
import { useToken } from "@/hooks/useToken";
import { setThemeValue } from "@/stores/public";
import { setMenuList } from "@/stores/menu";
import { getMenuList } from "@/servers/system/menu";
import { permissionsToArray } from "@/utils/permissions";
import { setPermissions, setUserInfo } from "@/stores/user";
import { useCommonStore } from "@/hooks/useCommonStore";
// import { getPermissions } from '@/servers/permissions';
import { getFirstMenu } from "@/menus/utils/helper";
import "./login.less";
import codePng from "@/assets/images/login/code.png";
import arowPng from "@/assets/images/login/arow.png";
import logoPng from "@/assets/images/login/logo.png";
import logoWhitePng from "@/assets/images/login/logo-white.png";
function Login() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const dispatch: AppDispatch = useDispatch();
  const [getToken, setToken] = useToken();
  const [isLoading, setLoading] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const { permissions, menuList } = useCommonStore();
  const themeCache = (localStorage.getItem(THEME_KEY) ||
    "default") as ThemeType;
  const suffix = <img src={codePng} alt="" />;
  useEffect(() => {
    if (!themeCache) {
      localStorage.setItem(THEME_KEY, "default");
    }
    if (themeCache === "dark") {
      document.body.className = "theme-dark";
    }
    if (themeCache === "ga") {
      document.body.className = "theme-ga";
    }
    dispatch(setThemeValue(themeCache || "default"));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [themeCache]);

  useEffect(() => {
    // 如果存在token，则直接进入页面
    if (getToken()) {
      // 如果不存在缓存则获取权限
      if (!permissions.length) {
        getUserPermissions();
      } else {
        // 有权限则直接跳转
        handleGoMenu(permissions);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  /** 获取用户权限 */
  const getUserPermissions = async () => {
    try {
      setLoading(true);
      // const { code, data } = await getPermissions({ refresh_cache: false });
      // if (Number(code) !== 200) return;
      // const { user, permissions } = {
      //   user: {
      //     id: 1
      //   },
      //   permissions: ['admin']
      // };
      const user = {
        email: "<EMAIL>",
        id: 1,
        phone: "123456789",
        username: "UCD",
      };
      const permissions = [
        {
          id: "dashboard",
          operation: [],
        },
        {
          id: "configuration",
          operation: [],
        },
        {
          id: "demo",
          operation: [
            "copy",
            "editor",
            "wangEditor",
            "virtualScroll",
            "watermark",
          ],
        },
        {
          id: "authority/user",
          operation: [
            "index",
            "create",
            "update",
            "view",
            "delete",
            "authority",
          ],
        },
        {
          id: "authority/role",
          operation: ["index", "create", "update", "view", "delete"],
        },
        {
          id: "authority/menu",
          operation: ["index", "create", "update", "view", "delete"],
        },
        {
          id: "content/article",
          operation: ["index", "create", "update", "view", "delete"],
        },
      ];
      const newPermissions = permissionsToArray(permissions);
      dispatch(setUserInfo(user));
      dispatch(setPermissions(newPermissions));
      handleGoMenu(newPermissions);
    } finally {
      setLoading(false);
    }
  };

  /** 获取菜单数据 */
  const getMenuData = async () => {
    if (menuList?.length) return menuList;
    let result: SideMenu[] = [];

    try {
      setLoading(true);
      const { code, data } = await getMenuList();
      if (Number(code) !== 200) return;
      dispatch(setMenuList(data || []));
      result = data;
    } finally {
      setLoading(false);
    }

    return result;
  };

  /** 菜单跳转 */
  const handleGoMenu = async (permissions: string[]) => {
    let menuData: SideMenu[] = menuList;
    if (!menuData?.length) {
      menuData = (await getMenuData()) as SideMenu[];
    }

    // 有权限则直接跳转
    const firstMenu = getFirstMenu(menuData, permissions);
    if (!firstMenu) {
      return messageApi.error({
        content: t("login.notPermissions"),
        key: "permissions",
      });
    }
    navigate(firstMenu);
  };

  /**
   * 处理登录
   * @param values - 表单数据
   */
  const handleFinish: FormProps["onFinish"] = async (values: LoginData) => {
    try {
      setLoading(true);
      console.log("登录data",values);
      const { status, data } = await login(values);
      console.log("登录data",data);
      
      if (Number(status) !== 200) return;
      const newData = {
        token: data?.token,
        user: {
          id: 1,
          username: values.username,
          email: "<EMAIL>",
          phone: "123456789",
        },
        permissions: [
          {
            id: "dashboard",
            operation: [],
          },
          {
            id: "test",
            operation: [],
          },
          {
            id: "configuration",
            operation: [],
          },
          {
            id: "llms",
            operation: [],
          },
          {
            id: "demo",
            operation: [
              "copy",
              "editor",
              "wangEditor",
              "virtualScroll",
              "watermark",
            ],
          },
          {
            id: "authority/user",
            operation: [
              "index",
              "create",
              "update",
              "view",
              "delete",
              "authority",
            ],
          },
          {
            id: "authority/role",
            operation: ["index", "create", "update", "view", "delete"],
          },
          {
            id: "authority/menu",
            operation: ["index", "create", "update", "view", "delete"],
          },
          {
            id: "content/article",
            operation: ["index", "create", "update", "view", "delete"],
          },
        ],
      };
      const { token, user, permissions } = newData;
      console.log("登录用户",newData);
      if (!permissions?.length || !token) {
        return messageApi.error({
          content: t("login.notPermissions"),
          key: "permissions",
        });
      }

      const newPermissions = permissionsToArray(permissions);
      setToken(token);
      dispatch(setUserInfo(user));
      dispatch(setPermissions(newPermissions));
      handleGoMenu(newPermissions);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 处理失败
   * @param errors - 错误信息
   */
  const handleFinishFailed: FormProps["onFinishFailed"] = (errors) => {
    console.error("errors:", errors);
  };

  return (
    <>
      {contextHolder}
      <div
        className={`
        ${themeCache === "dark" ? "login-bg-dark text-white" : "login-bg-light"}
        login-content
      `}
      >
        <div
          className={`
          login-title 
        ${themeCache === "dark" ? "bg-black" : "bg-white"}
        `}
        >
          <img
            className="login-logo"
            src={`${themeCache === "dark" ? logoWhitePng : logoPng}`}
            alt=""
          />
        </div>

        <div
          className={`
          login-body
        `}
        >
          <div className="login-text">
            <div className="text-name">{t("login.name")}</div>
            <div className="text-info">
              <span>NSFOCUS</span> LLM SECURITY TESTING AND EVALUATION SERVICE
            </div>
            <div>
              <img className="text-arow" src={arowPng} alt="" />
            </div>
          </div>
          <div className="login-box">
            <div className="box-title">{t("login.login")}</div>
            <Form
              onFinish={handleFinish}
              onFinishFailed={handleFinishFailed}
              initialValues={{
                username: "admin",
                password: "5.V7Ve_6mzX2m2tGx-Z27x1_RtnH86JP",
              }}
            >
              <Form.Item
                name="username"
                rules={[
                  {
                    required: true,
                    message: t("public.pleaseEnter", {
                      name: t("login.username"),
                    }),
                  },
                ]}
              >
                <Input
                  size="large"
                  allow-clear="true"
                  data-test="username"
                  autoComplete="username"
                />
              </Form.Item>

              <Form.Item
                name="password"
                rules={[
                  {
                    required: true,
                    message: t("public.pleaseEnter", {
                      name: t("login.password"),
                    }),
                  },
                  //PASSWORD_RULE(t),
                ]}
              >
                <Input size="large" />
              </Form.Item>
              <Form.Item
                name="passnode"
                rules={[
                  {
                    required: true,
                    message: t("public.pleaseEnter", {
                      name: t("login.passnode"),
                    }),
                  },
                ]}
              >
                <Input size="large" suffix={suffix} />
              </Form.Item>

              <Form.Item>
                <Button
                  style={{
                    width: "100%",
                    marginTop: "28px",
                  }}
                  size="large"
                  type="primary"
                  htmlType="submit"
                  className="w-full mt-5px rounded-5px tracking-2px"
                  loading={isLoading}
                >
                  {t("login.login")}
                </Button>
              </Form.Item>
            </Form>
          </div>
        </div>
        <div
          className={`
          login-footer 
        ${themeCache === "dark" ? "footer-dark" : "footer-light"}
        `}
        >
          {t("login.copyright")}
        </div>
      </div>
    </>
  );
}

export default Login;
