
import { request } from '@/servers/request';

/** API基础路径 */
const API_BASE = "/api"


//获取字典列表
export function getDictList(
  data: {
    "limit": number,
    "offset": number,
    "module"?: string,
    "keyword": string,
    "parent_id"?: string,
  },
  options?: { [key: string]: any },
) {
  return request.post<API.DictList>(API_BASE + '/dict/list',
    data);

}

//获取字典详情
export function getDictDetail(
  id: string,

) {

  const url = API_BASE + `/dict/${id}`;
  return request.get<Record<string, any>>(url);
}

//删除字典
export function removeDict(
  id: string,
  force?: boolean,
) {
  const url = API_BASE + `/dict/${id}`;
  const requestConfig: any = {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
  };

  // 如果有 force 参数，作为查询参数传递
  if (force) {
    requestConfig.params = { force };
  }

  return request.delete<Record<string, any>>(url, requestConfig);
}


//获取字典树
export function getDictTree(
  parent_id: string,
) {
  const url = API_BASE + `/dict/nodes`;
  return request.post<API.Category[]>(url,
    {
      parent_id,
    }
  );
}
//获取用例列表
export function getUseCaseList(
  data: {
    limit: number,
    offset: number,
    classification_id?: string,
    keyword?: string,
    strategy_id?: string,
  },
) {
  return request.post<API.UsecaseList>(API_BASE + '/usecase/list',
    data
  );
}

//获取用例详情
export function getUseCaseDetail(
  params: { id: string },
) {
  const url = API_BASE + `/usecase/${params.id}`;
  return request.get(url);
}

//创建用例
export function createUseCase(
  data: {
    payload: string[],
    evaluate_method: API.EvaluateMethod[],
    classfications: API.Classifications[],
  },
  options?: { [key: string]: any },
) {
  return request.post<API.UsecaseList>(API_BASE + '/usecase/create',data);
}

//更新用例
export function updateUseCase(
  data: {
    id: string,
    payload: string[],
    evaluate_method: API.EvaluateMethod[],
    classfications: API.Classification[],
  },
  options?: { [key: string]: any },
) {
  const url = API_BASE + `/usecase/${data.id}`;
  return request.put<API.UsecaseList>(url,
    data
  );
}

//删除用例
export function removeUseCase(options: { id: string }) {
  const url = API_BASE + `/usecase/${options.id}`;
  return request.delete<Record<string, any>>(url);
}

//获取统计数据
export function getStatistics(
  id: string,
) {
  const url = API_BASE + `/dict/statistics/${id}`;
  return request.get(url);
}
