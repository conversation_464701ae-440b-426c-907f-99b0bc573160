import { useState, useRef, useEffect } from 'react';
import { message, Tree, Tabs, Card, Row, Col, Statistic, Input, ConfigProvider, Popconfirm, Modal, Form, Select, Button, Drawer, Descriptions, Space, Layout, Menu } from 'antd';
import type { TreeProps } from 'antd';
import { DownOutlined, FileOutlined, LikeOutlined, ProfileOutlined, ProjectOutlined } from '@ant-design/icons';
import { ProTable } from '@ant-design/pro-table';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import { PageContainer } from '@ant-design/pro-layout';

import { getUseCaseList, removeUseCase, getUseCaseDetail, updateUseCase, getDictTree, getDictDetail, getStatistics } from '@/services/Datacases/api';

import Meta from 'antd/es/card/Meta';
import PieChart from '@/components/PieChart';
import { useParams } from '@umijs/max';
import { ModalForm, ProDescriptions, ProDescriptionsItemProps, ProFormText, ProFormTextArea, StatisticCard } from '@ant-design/pro-components';
import { set } from 'lodash';
const { Header, Content, Footer, Sider } = Layout;

import { Divider, Typography } from 'antd';

const { Title, Paragraph, Text, Link } = Typography;
interface TreeNode {
  name: string;
  subclass?: Record<string, any>;  // 修改为字典类型
}

interface TreeDataNode {
  title: string;
  key: string;
  children?: TreeDataNode[];
}

const processNode = (parentKey: string, node: TreeNode): TreeDataNode => {
  if (!node.subclass || Object.keys(node.subclass).length === 0) {
    return {
      title: node.name,
      key: parentKey + '.' + node.name,
    };
  }

  return {
    title: node.name,
    key: node.name,
    children: Object.keys(node.subclass).map(subKey => {
      const childNode = processNode(parentKey + '.' + subKey, node.subclass![subKey]);
      return {
        title: subKey,
        key: parentKey + '.' + subKey,
        children: childNode.children
      };
    }),
  };
};

export default function Detail() {
  const [currentRow, setCurrentRow] = useState<API.LLMItem>();
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [prompts, setPrompts] = useState<API.UsecaseItem[]>([]); // 添加prompts状态
  const [cuurrentTotal, setCurrentTotal] = useState<number>(0); // 添加当前总数状态
  const actionRef = useRef<ActionType>();
  const [editModalVisible, setEditModalVisible] = useState<boolean>(false);
  const [editingUseCase, setEditingUseCase] = useState<API.UsecaseItem | null>(null);
  const [editForm] = Form.useForm();
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [showData, setShowData] = useState<any[]>([]);
  const [showstatistics, setShowStatistics] = useState<any[]>([]); // 用于存储统计数据
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [treeData, setTreeData] = useState<TreeDataNode[]>([]); // 用于存储树形数据
  const [loading, setLoading] = useState<boolean>(false);


  //获取路由参数
  const { id } = useParams<{ id: string }>();

  // 定义树形数据转换函数
  const transformData = (nodes: API.Category[]): any[] => {
    return nodes.map(node => ({
      title: node.name,
      key: node.id,
      children: node.children && node.children.length > 0
        ? transformData(node.children)
        : [],
      raw: node
    }));
  };

  // 获取树形数据的函数
  const fetchTreeData = async (parent_id: string) => {
    try {
      setLoading(true);
      // 直接获取当前节点的直接子节点，不进行递归
      const res = await getDictTree(parent_id);
      console.log('获取树形数据:', parent_id, res);

      if (res && res.status === 200 && res.data) {
        const nodes = res.data;
        const transformedData = transformData(nodes);

        // 更新树形数据，将子节点添加到对应的父节点
        setTreeData(transformedData);
      }

    } catch (error) {
      console.error('获取树形数据失败:', error);
      message.error('获取树形数据失败');
    } finally {
      setLoading(false);
    }
  };


  // 递归查找匹配节点
  const findMatchNodes = (data: any[], keyword: string): string[] => {
    const result: string[] = [];
    data.forEach(item => {
      if (item.title.includes(keyword)) {
        result.push(item.key);
      }
      if (item.children) {
        result.push(...findMatchNodes(item.children, keyword));
      }
    });
    return result;
  };

  // Tree 节点选择时更新表格数据和统计数据
  const onSelect = async (keys: string[]) => {
    setSelectedKeys(keys);

    if (keys.length > 0) {
      const selectedId = keys[0] as string;
      console.log('选中的节点ID:', selectedId);

      try {
        // 并行获取用例列表和统计数据
        const [useCaseResponse, statisticsResponse] = await Promise.all([
          getUseCaseList({ classification_id: selectedId}),
          getStatistics(selectedId)
        ]);

        // 更新用例列表数据
        if (useCaseResponse?.data) {
          setPrompts(useCaseResponse.data);
          setCurrentTotal(useCaseResponse.total || 0); // 更新当前总数
        }

        // 更新统计数据
        if (statisticsResponse?.status === 200) {
          setShowStatistics(statisticsResponse.data);
        }

      } catch (error) {
        console.error('获取数据失败:', error);
        message.error('获取数据失败');
      }
    } else {
      // 如果没有选中节点，清空数据
      setPrompts([]);
      // 可选：恢复到初始统计数据
      if (id) {
        fetchStatistics(id);
      }
    }
  };

  // 在组件挂载获取数据
  useEffect(() => {
    if (id) {
      fetchDictInfo(id); // 使用路由参数中的 id
      fetchStatistics(id); // 获取统计数据
      fetchTreeData(id); // 获取树形数据
      onSelect([id]); // 默认选中当前字典的 ID
    }
  }, [id]);


  // 添加处理模态框开关的函数
  const handleModalOpen = (open: boolean) => {
    setModalOpen(open);
    if (!open) {
      setCurrentRow(undefined);
    }
  };

  // 修改 onEdit 函数
  const onEdit = (record: API.UsecaseItem) => {
    setCurrentRow(record);
    setModalOpen(true);
  };

  // 修改 handleUpdateUseCase 函数
  const handleUpdateUseCase = async (values: any) => {
    try {
      if (!currentRow) return false;

      const updateData = {
        id: currentRow.id,
        payload: Array.isArray(values.payload) ? values.payload : [values.payload],
      };

      const response = await updateUseCase(updateData);
      if (response.status === 200) {
        message.success('用例更新成功');
        setModalOpen(false);
        setCurrentRow(undefined);


        // 刷新表格数据

        setPrompts(prev => prev.map(item => item.id === currentRow.id ? { ...item, ...updateData } : item));
        actionRef.current?.reload();

        // 刷新当前选中节点的统计数据
        if (selectedKeys.length > 0) {
          const selectedId = selectedKeys[0] as string;
          fetchStatistics(selectedId);

        }


        return true;
      } else {
        message.error('用例更新失败');
        return false;
      }
    } catch (error) {
      console.error('更新用例失败:', error);
      message.error('更新用例失败');
      return false;
    }
  };
  // 添加 handleView 函数
  const handleView = (record: API.UsecaseItem) => {
    setCurrentRow(record);
    setShowDetail(true);
  };

  //删除用例
  const onDelete = async (record: API.UsecaseItem) => {
    try {
      const res = await removeUseCase({ id: record.id });
      if (res) {
        message.success('删除成功');
        // 刷新表格
        actionRef.current?.reload();
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };



  // 处理搜索事件
  const handleSearch = (value: string) => {
    setSearchKeyword(value);
    const matchKeys = findMatchNodes(treeData, value);
    if (matchKeys.length > 0) {
      setSelectedKeys([matchKeys[0]]);
      onSelect([matchKeys[0]]); // 这样会自动触发统计数据更新
    } else {
      setSelectedKeys([]);
      setPrompts([]);
      // 恢复到根节点的统计数据
      if (id) {
        fetchStatistics(id);
      }
    }
  };



  //获取统计数据
  const fetchStatistics = async (id: string) => {
    try {
      const res = await getStatistics(id);
      if (res.status === 200) {
        // console.log('获取统计数据:', res.data);

        setShowStatistics(res.data);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
      message.error('获取统计数据失败');
    }
  }
  //获取字典信息
  const fetchDictInfo = async (id: string) => {
    try {
      const res = await getDictDetail(id);

      if (res.status === 200) {

        setShowData(res.data);

      } else {
        message.error('获取字典信息失败');
      }

    } catch (error) {
      console.error('获取字典信息失败:', error);
      message.error('获取字典信息失败');
    }
  }
  // 定义右侧表格的列
  const columns: ProColumns<API.UsecaseItem>[] = [
    //序号
    {
      title: '序号',
      dataIndex: 'id',
      valueType: 'indexBorder',
      width: 60,
      hideInSearch: true,
    },
    //排名列
    // {
    //   title: 'ID',
    //   dataIndex: 'id',
    //   valueType: 'indexBorder',
    //   width: 50,
    //   hideInSearch: true,
    // },
    // 模型名称
    {
      title: 'Prompt',
      dataIndex: 'payload',
      ellipsis: true,
      copyable: true,
      hideInSearch: true,
      width: '60%'
    },
    // 路径
    {
      title: '路径',
      dataIndex: 'classification',
      ellipsis: true,
      hideInSearch: true,
      render: (text, record) => {
        // 显示 classifications.parent_name
        if (record.classification && record.classification.parent_name) {
          console.log('record.classifications:', record.classification);
          
          return record.classification.parent_name;
        }
        return '-';

      },
    },
    {
      title: '数据集来源',
      dataIndex: 'classification',
      hideInSearch: true,
      // render: (text, record) => {
      //   // 显示完整的 path 路径
      //   if (record.classifications && record.classifications.path && Array.isArray(record.classifications.path)) {
      //     return record.classifications.path.map(item => item.name).join(' > ');
      //   }
      //   return '-';
      // },
      render: (text, record) => {
        // 显示完整的 path 路径
        if (record.classification && record.classification.path && Array.isArray(record.classification.path)) {
          return record.classification.path.map(item => item.name).join(' > ') + ' > ' + record.classification.name;
        }
        return '-';
      },
    },

    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      render: (text, record, _, action) => [
        <a
          key="view"
          onClick={() => handleView(record)}
        >
          查看
        </a>,
        <a key="editable" onClick={() =>
          onEdit(record)
        }>
          编辑
        </a>,
        <Popconfirm
          key="deletable"
          title="确定删除该用例？"
          onConfirm={() => onDelete(record)}
        >
          <a>删除</a>
        </Popconfirm>
      ],
    },
  ];
  return (
    <PageContainer title={false}>

      <Layout
        style={{}}
      >
        <Sider style={{ background: '#FFF'}} width={350}>
          <Tabs>
            <Tabs.TabPane
              tab={<span><ProjectOutlined /> 用例集 - {showData.name}</span>}
              key="dataset"
            />
          </Tabs>
          <Input.Search
            placeholder="输入搜索关键字"
            style={{ marginBottom: 16 }}
            // 绑定搜索事件
            onSearch={handleSearch}
            // style={{paddingLeft:2}}
          />

          <Tree
            showLine
            // switcherIcon={<DownOutlined />}
            treeData={treeData}
            height={1200}
            selectedKeys={selectedKeys}
            onSelect={onSelect}
            style={{paddingLeft:2}}
          />
        </Sider>
        <Content style={{ padding: '0 24px', minHeight: 280, background: '#FFF' }}>
          {/* <Card size='small' style={{ minHeight: 210 }}> */}
          <Row style={{ marginBottom: 10, marginTop: 10 }}>
            <Col span={12}>

              <StatisticCard.Group direction={'row'} style={{ marginBottom: 10, marginTop: 50 }}>
                <StatisticCard
                  statistic={{
                    title: showstatistics?.name,
                    value: showstatistics?.total_usecases_count,
                    icon: (
                      <img
                        // style={imgStyle}
                        src="https://gw.alipayobjects.com/mdn/rms_7bc6d8/afts/img/A*dr_0RKvVzVwAAAAAAAAAAABkARQnAQ"
                        alt="icon"
                      />
                    ),
                  }}
                />
                <StatisticCard
                  statistic={{
                    title: '子分类',
                    value: showstatistics?.sub_dicts_count,
                    icon: (
                      <img
                        // style={imgStyle}
                        src="https://gw.alipayobjects.com/mdn/rms_7bc6d8/afts/img/A*-jVKQJgA1UgAAAAAAAAAAABkARQnAQ"
                        alt="icon"
                      />
                    ),
                  }}
                />

              </StatisticCard.Group>
            </Col>
            <Col span={12} style={{ textAlign: 'right' }}>
              <PieChart data={showstatistics.sub_dicts} />
            </Col>
          </Row>

          <Paragraph>
            <pre> {showData.description || '暂无描述'} </pre>
          </Paragraph>
          {/* <Card>
              <Meta
                description={showData.description || '暂无描述'}
              />
            </Card> */}
          {/* </Card> */}
            <ProTable<API.UsecaseItem, API.PageParams>
             headerTitle={`用例列表`}
              style={{ minHeight: '70vh' }}
              columns={columns}
              actionRef={actionRef}
              dataSource={prompts}
              search={false}
              request={async (params) => {
                try {
                  const body = {
                    ...params,
                    limit: params.pageSize || 10,
                    offset: ((params.current || 1)) * (params.pageSize || 10),
                    classification_id: id || id,
                    keyword: params.keyword || '',
                  }


                  const res = await getUseCaseList(body);

                  if (res.status === 200) {
                    // console.log('请求成功:', res.data);

                    return {
                      data: res.data || [],
                      total: res.total || 0,
                      success: true
                    };
                  }
                  return { data: [], success: false, total: 0 };
                }
                catch (error) {
                  console.error('请求失败:', error);
                  return { data: [], success: false, total: 0 };
                }
              }}
              pagination={{
                pageSize: 10,
                total: cuurrentTotal || 0,
                showTotal: (total, range) => `${range[0]}-${range[1]} of ${total}`,
              }}
            />


        </Content>
      </Layout>

      <ModalForm
        title='编辑用例'
        width="800px"
        open={modalOpen}
        onOpenChange={handleModalOpen}
        // 关闭弹窗时，清空 currentRow
        modalProps={{
          destroyOnClose: true,
        }}
        initialValues={{
          ...currentRow
        }}
        onFinish={handleUpdateUseCase}
      >
        <ProFormTextArea
          label="Prompt内容"
          name="payload"
          fieldProps={{ rows: 20 }}
          placeholder="请输入Prompt内容"
          rules={[
            {
              required: true,
              message: '请输入Prompt内容',
            },
          ]}
        />
      </ModalForm>
      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.id && (
          <ProDescriptions<API.UsecaseItem>
            column={2}
            title={currentRow?.payload?.[0] || '用例详情'}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={[
              {
                title: 'ID',
                dataIndex: 'id',
                render: (text) => text || '-',
              },
              {
                title: 'Prompt内容',
                dataIndex: 'payload',
                render: (text) => {
                  if (Array.isArray(text)) {
                    return text.join(', ');
                  }
                  return text || '-';
                },
              },
              {
                title: '路径',
                dataIndex: 'classifications',
                render: (text, record) => {
                  // 显示 classifications.name
                  if (record.classifications && record.classifications.name) {
                    return record.classifications.name;
                  }
                  return '-';
                },
              },
              {
                title: '数据集来源',
                dataIndex: 'classifications',
                render: (text, record) => {
                  // 显示完整的 path 路径
                  if (record.classifications && record.classifications.path && Array.isArray(record.classifications.path)) {
                    return record.classifications.path.map(item => item.name).join(' > ');
                  }
                  return '-';
                },
              },
              {
                title: '创建时间',
                dataIndex: 'create_time',
                render: (text) => text ? new Date(text).toLocaleString() : '-',
              },
              {
                title: '更新时间',
                dataIndex: 'update_time',
                render: (text) => text ? new Date(text).toLocaleString() : '-',
              },
            ] as ProDescriptionsItemProps<API.UsecaseItem>[]}
          />
        )}
      </Drawer>

    </PageContainer>
  );
}


